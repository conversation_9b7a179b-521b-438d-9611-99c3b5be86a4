.tickets-list-container {
  // Remove bottom margin from last ticket to eliminate extra space
  .ticket-wrap:last-child {
    margin-bottom: 0 !important;
  }

  // Enable smooth scrolling for auto-scroll functionality
  scroll-behavior: smooth;

  // Remove overflow since parent container handles scrolling
  // This prevents double scroll bars
  overflow: visible;
  @media (max-width: 1260px) {
    .status-filter-select {
      .select__control {
        min-width: max-content;
      }
    }
  }
}

.ticket-list-container-wrap {
  padding-bottom: var(--spacing-none);
}

.ticket-wrap {
  width: 100%;
  border: 1px solid var(--border-color-light-gray);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  position: relative;
  background-color: var(--color-white);
  margin-bottom: var(--spacing-md);
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover:not(.selected) {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-0.5px);
  }

  &.selected {
    border: 2px solid var(--color-primary);
    box-shadow: 0 4px 12px rgba(19, 94, 150, 0.2);
    transform: translateY(-1px);
    transition: all 0.2s ease;
  }

  // New styling for status and delete icon container
  // .status-delete-wrap {
  // }
  .delete-icon {
    font-size: var(--icon-size-sm);
    fill: var(--icon-bold-red-color);
  }
  .edit-icon {
    font-size: var(--icon-size-sm);
  }
  .id-name-wrap {
    gap: var(--spacing-lg);
    cursor: pointer;
    padding: var(--spacing-md) 0px;

    .id-wrap {
      color: var(--color-dark-50);
      border-bottom: 1px dashed var(--color-light-grayish-blue);
      line-height: var(--line-height-base);
    }
  }

  .heading-text-wrap {
    font-weight: var(--font-weight-medium);
  }

  .description-wrap {
    padding: var(--spacing-xs) 0;

    .description-text {
      color: var(--color-dark-50);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-relaxed);
    }
  }

  .name-wrap {
    color: var(--color-dark-50);
    max-width: 155px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .name-wrap {
    color: var(--color-dark-50);
    max-width: 155px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .date-wrap {
    padding: var(--spacing-xs) 0;

    .date-text {
      color: var(--color-dark-50);
      font-size: var(--font-size-sm);
    }
  }

  .category-wrap {
    padding: var(--spacing-xs) 0;

    .category-text {
      color: var(--color-dark-50);
      font-size: var(--font-size-sm);
    }
  }

  .status-badge-wrap {
    padding: var(--spacing-xs) 0;
  }

  .name-time-wrap {
    .user-icon {
      font-size: var(--font-size-md);
      color: var(--color-dark-50);
    }

    .time-icon {
      font-size: var(--font-size-md);
      color: var(--color-dark-50);
    }

    .name-text {
      color: var(--color-dark);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
    }

    .time-text {
      color: var(--color-dark-50);
      font-size: var(--font-size-sm);
    }
  }

  .assigned-wrap {
    padding: var(--spacing-xs) 0;

    .assigned-text {
      color: var(--color-dark-50);
      font-size: var(--font-size-sm);

      .assigned-label {
        font-weight: var(--font-weight-medium);
      }
    }
  }

  .time-wrap {
    color: var(--color-dark-50);

    .timer-icon {
      height: var(--font-size-sm);
      width: var(--font-size-sm);
      fill: var(--color-primary);
    }
  }

  .profile-wrap {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-lg);
    line-height: 0px;

    .profile-image {
      height: 35px;
      width: 35px;
      border-radius: var(--border-radius-full);
    }
  }

  .ticket-status-select {
    .MuiFormControl-root {
      width: auto;

      .MuiSvgIcon-root {
        height: var(--icon-size-sm);
        width: var(--icon-size-sm);
        margin-top: var(--spacing-xs);
      }
    }

    .MuiInputBase-root {
      font-family: var(--font-family-primary) !important;
    }

    .MuiSelect-select {
      padding: var(--spacing-tiny) 0px 0px var(--spacing-md);
      font-size: var(--font-size-sm) !important;
    }

    fieldset {
      height: 25px !important;
      border-radius: var(--border-radius-xs);
    }
  }

  // Minimal fix to ensure global chip styles are applied to Typography components
  .MuiTypography-root {
    &.success,
    &.failed,
    &.draft,
    &.status-yellow,
    &.active-onboarding,
    &.ongoing,
    &.closed {
      display: inline-block !important;
    }
  }
}
