'use client';

import React, { useState, useMemo, useEffect, useContext } from 'react';
import { Box, Typography, Tooltip, Divider } from '@mui/material';
import { useTheme, useMediaQuery } from '@mui/material';
import { useRouter } from 'next/navigation';
import {
  fetchFromStorage,
  saveToStorage,
  removeFromStorage,
} from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import {
  checkOrganizationRole,
  setApiMessage,
} from '@/helper/common/commonFunctions';
import AuthContext from '@/helper/authcontext';
import Ticket from '../Ticket';
import CustomSearch from '@/components/UI/CustomSearch';
import FilterListIcon from '@mui/icons-material/FilterList';
import CheckIcon from '@mui/icons-material/Check';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
import AddIcon from '@mui/icons-material/Add';
import RightDrawer from '@/components/UI/RightDrawer';
import FilterComponent from '@/components/UI/FilterComponent';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomButton from '@/components/UI/CustomButton';
import ContentLoader from '@/components/UI/ContentLoader';
import NoDataView from '@/components/UI/NoDataView';
import CustomOrgPagination from '@/components/UI/customPagination';
import { supportTicketService } from '@/services/supportTicketService';
import { staticOptions } from '@/helper/common/staticOptions';
import './alltickets.scss';

export default function AllTicketsList() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down(1500));
  const { userdata, setUserdata } = useContext(AuthContext);
  const [searchValue, setSearchValue] = useState('');
  const [openFilterDrawer, setOpenFilterDrawer] = useState(false);
  const [loading, setLoading] = useState(true);
  const [ticketsData, setTicketsData] = useState([]);

  // Pagination state - following Allergen pattern
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  const router = useRouter();

  // Filter related state - following recipe module pattern
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [filterData, setFilterData] = useState({
    status: '',
    priority: '',
    module: '',
    type: '',
  });

  // Separate state for applied filters - following recipe module pattern
  const [filterDataApplied, setFilterDataApplied] = useState({
    status: '',
    priority: '',
    module: '',
    type: '',
    searchValue: '',
  });
  const [isRestoringFilters, setIsRestoringFilters] = useState(false);

  // API function to get tickets with filters - following recipe module pattern
  const getTicketsListData = async (
    search,
    page,
    limit,
    apiFilters,
    showLoader = true
  ) => {
    if (showLoader) {
      setLoading(true);
    }

    try {
      // Build filter object for API
      const filterParams = {
        ticket_status: apiFilters?.status || '',
        ticket_priority: apiFilters?.priority || '',
        ticket_module: apiFilters?.module || '',
        ticket_type: apiFilters?.type || '',
        assigned_to_user_id: apiFilters?.assigned_to_user_id || '',
      };
      Object.keys(filterParams)?.forEach((key) => {
        if (
          filterParams[key] === undefined ||
          filterParams[key] === '' ||
          filterParams[key] === null
        ) {
          delete filterParams[key];
        }
      });

      const response = await supportTicketService.getTicketsList(
        search || '',
        page || 1,
        limit || 10,
        filterParams,
        null // sort - can be added later if needed
      );
      if (response?.tickets) {
        setTicketsData(response?.tickets);
        setTotalCount(response?.totalCount || 0);
      } else {
        setTicketsData([]);
        setTotalCount(0);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setTicketsData([]);
      setTotalCount(0);
    } finally {
      if (showLoader) {
        setTimeout(() => {
          setLoading(false);
        }, 100);
      }
    }
  };

  // Load saved filters from localStorage on mount
  useEffect(() => {
    const savedFilters = fetchFromStorage(identifiers?.SUPPORT_TICKET_FILTER);
    if (!savedFilters) {
      setSelectedFilters(filters?.slice(0, 4)?.map((filter) => filter?.key));
    } else {
      setSelectedFilters(savedFilters);
    }
  }, []);

  // Filter persistence functionality - following staff module pattern
  useEffect(() => {
    if (
      fetchFromStorage(identifiers?.RedirectData) &&
      fetchFromStorage(identifiers?.RedirectData)?.IsFromUser &&
      !isRestoringFilters
    ) {
      setIsRestoringFilters(true);
      const fdata = fetchFromStorage(identifiers?.RedirectData);
      setCurrentPage(fdata?.page);
      setFilterData(fdata?.filterData);
      setFilterDataApplied(fdata?.filterData);
      setSearchValue(fdata?.searchValue);
      setRowsPerPage(fdata?.rowsPerPage);
      getTicketsListData?.(
        fdata?.searchValue,
        fdata?.page,
        fdata?.rowsPerPage,
        fdata?.filterData,
        true
      );
    } else if (userdata && userdata?.IsFromUser && !isRestoringFilters) {
      setIsRestoringFilters(true);
      const fdata = userdata;
      setCurrentPage(fdata?.page);
      setFilterData(fdata?.filterData);
      setFilterDataApplied(fdata?.filterData);
      setSearchValue(fdata?.searchValue);
      setRowsPerPage(fdata?.rowsPerPage);
      getTicketsListData?.(
        fdata?.searchValue,
        fdata?.page,
        fdata?.rowsPerPage,
        fdata?.filterData,
        true
      );
    } else if (
      !fetchFromStorage(identifiers?.RedirectData) &&
      !userdata?.IsFromUser
    ) {
      setUserdata();
      removeFromStorage(identifiers?.RedirectData);
    }
  }, [
    fetchFromStorage(identifiers?.RedirectData)?.IsFromUser,
    userdata?.IsFromUser,
  ]);

  // Initial load effect - following staff module pattern
  useEffect(() => {
    if (
      !fetchFromStorage(identifiers?.RedirectData) &&
      userdata?.page === undefined &&
      fetchFromStorage(identifiers?.RedirectData)?.IsFromUser === undefined &&
      userdata?.IsFromUser === undefined
    ) {
      getTicketsListData?.('', 1, rowsPerPage, {}, true);
    }
  }, []);

  // Filter configuration
  const filters = useMemo(
    () => [
      {
        key: 'search',
        label: 'Search',
        options: [],
        permission: true,
      },
      {
        key: 'status',
        label: 'Status',
        options: staticOptions.SUPPORT_TICKET_STATUS_OPTIONS,
        permission: true,
      },
      {
        key: 'priority',
        label: 'Priority',
        options: staticOptions.SUPPORT_TICKET_PRIORITY_OPTIONS,
        permission: true,
      },
      {
        key: 'module',
        label: 'Module',
        options: staticOptions.SUPPORT_TICKET_MODULE_OPTIONS,
        permission: true,
      },
      {
        key: 'type',
        label: 'Type',
        options: staticOptions.SUPPORT_TICKET_TYPE_OPTIONS,
        permission: true,
      },
    ],
    []
  );

  const handleTicketClick = (ticket) => {
    // Save current filter state before navigating
    const redirectData = {
      IsFromUser: true,
      page: currentPage,
      filterData: filterDataApplied,
      searchValue: searchValue,
      rowsPerPage: rowsPerPage,
    };

    // Save to localStorage
    saveToStorage(identifiers?.RedirectData, redirectData);

    // Navigate to main support ticket page with ticket ID as query parameter
    router.push(`/support-ticket?id=${ticket?.id}`);
  };

  const handleTicketDelete = async () => {
    try {
      // Calculate the new total count
      const newTotalCount = Math.max(0, totalCount - 1);
      setTotalCount(newTotalCount);

      // Calculate if we need to adjust pagination
      const totalPages = Math.ceil(newTotalCount / rowsPerPage);

      // Prepare filters for API call
      const filters = {};
      if (filterDataApplied?.status) filters.status = filterDataApplied.status;
      if (filterDataApplied?.priority)
        filters.priority = filterDataApplied.priority;
      if (filterDataApplied?.module) filters.module = filterDataApplied.module;
      if (filterDataApplied?.type) filters.type = filterDataApplied.type;

      const searchValue = filterDataApplied?.searchValue || '';

      // If current page is beyond the new total pages, go to the last available page
      if (currentPage > totalPages && totalPages > 0) {
        // Navigate to the last page
        await getTicketsListData?.(
          searchValue,
          totalPages,
          rowsPerPage,
          filters,
          false
        );
        setCurrentPage(totalPages);
      } else if (ticketsData?.length === 1 && currentPage > 1) {
        // If current page will become empty and we're not on page 1, go to previous page
        await getTicketsListData?.(
          searchValue,
          currentPage - 1,
          rowsPerPage,
          filters,
          false
        );
        setCurrentPage(currentPage - 1);
      } else {
        // Refresh current page data
        await getTicketsListData?.(
          searchValue,
          currentPage,
          rowsPerPage,
          filters,
          false
        );
      }
    } catch (error) {
      console.error('Error handling ticket deletion:', error);
      // If there's an error, refresh the current page data
      const filters = {};
      if (filterDataApplied?.status) filters.status = filterDataApplied?.status;
      if (filterDataApplied?.priority)
        filters.priority = filterDataApplied?.priority;
      if (filterDataApplied?.module) filters.module = filterDataApplied?.module;
      if (filterDataApplied?.type) filters.type = filterDataApplied?.type;

      await getTicketsListData?.(
        filterDataApplied?.searchValue || '',
        currentPage,
        rowsPerPage,
        filters,
        false
      );
    }
  };

  //   const handleTicketClick = (ticket) => {
  //   // Only allow navigation for super_admin
  //   if (checkOrganizationRole('super_admin')) {
  //     router.push(`/support-ticket?id=${ticket.id}`);
  //   }
  // };

  // Filter functions - following recipe module pattern
  const toggleFilter = (key) => {
    setSelectedFilters((prevFilters) => {
      if (prevFilters?.includes(key)) {
        return prevFilters?.filter((item) => item !== key);
      } else {
        const index = filters.findIndex((filter) => filter?.key === key);
        const newFilters = [...prevFilters];
        newFilters?.splice(index, 0, key);
        return newFilters;
      }
    });
  };

  const getFirstFourFilters = () => {
    const savedFilters = fetchFromStorage(identifiers?.SUPPORT_TICKET_FILTER);
    setSelectedFilters(savedFilters?.slice(0, 4));
    saveToStorage(
      identifiers?.SUPPORT_TICKET_FILTER,
      savedFilters?.slice(0, 4)
    );
  };

  const saveLayout = () => {
    saveToStorage(identifiers?.SUPPORT_TICKET_FILTER, selectedFilters);
    setOpenFilterDrawer(false);
  };

  const handleKeyPress = (event) => {
    if (event?.key === 'Enter') {
      handleApplyFilter();
      setOpenFilterDrawer(false);
    }
  };

  const handleApplyFilter = () => {
    setIsRestoringFilters(false); // Reset flag when manually applying filters
    const newFilterData = {
      status: filterData?.status,
      priority: filterData?.priority,
      module: filterData?.module,
      type: filterData?.type,
      searchValue: searchValue,
    };
    setFilterDataApplied(newFilterData);
    setCurrentPage(1); // Reset to first page when applying filters

    // Call API directly with new filters - pass search value as first parameter
    const filters = {};
    if (newFilterData?.status) filters.status = newFilterData.status;
    if (newFilterData?.priority) filters.priority = newFilterData.priority;
    if (newFilterData?.module) filters.module = newFilterData.module;
    if (newFilterData?.type) filters.type = newFilterData.type;

    getTicketsListData?.(searchValue || '', 1, rowsPerPage, filters, true);

    if (isMobile) {
      setOpenFilterDrawer?.(false);
    }
  };

  const handleClearFilter = () => {
    setIsRestoringFilters(false); // Reset flag when manually clearing filters
    setFilterData?.({
      status: '',
      priority: '',
      module: '',
      type: '',
    });
    setSearchValue?.('');
    setFilterDataApplied({
      status: '',
      priority: '',
      module: '',
      type: '',
      searchValue: '',
    });
    setCurrentPage(1); // Reset to first page when clearing filters

    // Call API directly with cleared filters
    getTicketsListData?.('', 1, rowsPerPage, {}, true);

    if (isMobile) {
      setOpenFilterDrawer?.(false);
    }
  };

  // Pagination handlers - following Allergen pattern
  const handlePageChange = async (newPage) => {
    setCurrentPage(newPage);
    const filters = {};
    if (filterDataApplied?.status) filters.status = filterDataApplied.status;
    if (filterDataApplied?.priority)
      filters.priority = filterDataApplied.priority;
    if (filterDataApplied?.module) filters.module = filterDataApplied.module;
    if (filterDataApplied?.type) filters.type = filterDataApplied.type;

    await getTicketsListData?.(
      filterDataApplied?.searchValue || '',
      newPage,
      rowsPerPage,
      filters,
      false
    );
  };

  const handleRowsPerPageChange = async (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setCurrentPage(1);
    const filters = {};
    if (filterDataApplied?.status) filters.status = filterDataApplied.status;
    if (filterDataApplied?.priority)
      filters.priority = filterDataApplied.priority;
    if (filterDataApplied?.module) filters.module = filterDataApplied.module;
    if (filterDataApplied?.type) filters.type = filterDataApplied.type;

    await getTicketsListData?.(
      filterDataApplied?.searchValue || '',
      1,
      newRowsPerPage,
      filters,
      false
    );
  };

  const handleCreateTicket = () => {
    router?.push?.('/support-ticket/create-ticket');
  };

  // Handle ticket status change and refresh list
  const handleTicketStatusChange = async (ticketId, newStatus) => {
    try {
      // Update the specific ticket's status in the current list
      const updatedTickets = ticketsData?.map((ticket) =>
        ticket?.id === ticketId
          ? { ...ticket, ticket_status: newStatus }
          : ticket
      );
      setTicketsData(updatedTickets);

      // Refresh the list with current filters to get updated data from server
      const filters = {};
      if (filterDataApplied?.status) filters.status = filterDataApplied.status;
      if (filterDataApplied?.priority)
        filters.priority = filterDataApplied.priority;
      if (filterDataApplied?.module) filters.module = filterDataApplied.module;
      if (filterDataApplied?.type) filters.type = filterDataApplied.type;

      // Call API to refresh the list
      await getTicketsListData?.(
        filterDataApplied?.searchValue || '',
        currentPage,
        rowsPerPage,
        filters,
        false
      );
    } catch {
      // Handle error silently or show user-friendly message
      setApiMessage(
        'error',
        'Failed to refresh ticket list. Please try again.'
      );
    }
  };

  return (
    <Box className="section-wrapper">
      <Box className="section-right">
        <Box className="section-right-tab-header">
          <Box className="d-flex align-center justify-space-between all-ticket-header-wrap">
            <Box className="section-right-title d-flex align-center gap-sm">
              <Typography className="sub-header-text">
                All Support Tickets
              </Typography>
            </Box>

            {/* Search and Filter Section - Same as Support Ticket */}
            <Box className="d-flex align-center gap-sm all-ticket-filter-wrap">
              <Box className="search-section-wrap">
                {!isMobile &&
                  selectedFilters?.map((key) => {
                    const filter = filters?.find((f) => f?.key === key);
                    return filter?.permission ? (
                      <React.Fragment key={key}>
                        {key === 'search' ? (
                          <Box className="search-section-fields">
                            <CustomSearch
                              fullWidth
                              setSearchValue={setSearchValue}
                              onKeyPress={handleKeyPress}
                              searchValue={searchValue || ''}
                            />
                          </Box>
                        ) : (
                          <Box className="search-section-fields">
                            <CustomSelect
                              placeholder={filter?.label || ''}
                              options={filter?.options || []}
                              value={
                                filter?.options?.find((opt) => {
                                  return opt?.value === filterData?.[key];
                                }) || ''
                              }
                              onChange={(e) =>
                                setFilterData?.({
                                  ...filterData,
                                  [key]: e?.value || '',
                                })
                              }
                              menuPortalTarget={document?.body}
                              styles={{
                                menuPortal: (base) => ({
                                  ...base,
                                  zIndex: 9999,
                                }),
                              }}
                            />
                          </Box>
                        )}
                      </React.Fragment>
                    ) : null;
                  })}

                {!isMobile && (
                  <>
                    <Box>
                      <CustomButton
                        isIconOnly
                        startIcon={
                          <Tooltip
                            title={
                              <Typography className="sub-title-text">
                                Apply Filter
                              </Typography>
                            }
                            arrow
                            classes={{
                              tooltip: 'info-tooltip-container',
                            }}
                          >
                            <CheckIcon />
                          </Tooltip>
                        }
                        onClick={handleApplyFilter}
                      />
                    </Box>
                    <Box>
                      <CustomButton
                        variant="outlined"
                        isIconOnly
                        startIcon={
                          <Tooltip
                            title={
                              <Typography className="sub-title-text">
                                Clear Filter
                              </Typography>
                            }
                            arrow
                            classes={{
                              tooltip: 'info-tooltip-container',
                            }}
                          >
                            <ClearOutlinedIcon />
                          </Tooltip>
                        }
                        onClick={handleClearFilter}
                      />
                    </Box>
                  </>
                )}
              </Box>
              <CustomButton
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">
                        Filters
                      </Typography>
                    }
                    classes={{ tooltip: 'info-tooltip-container' }}
                    arrow
                  >
                    <FilterListIcon />
                  </Tooltip>
                }
                onClick={() => {
                  setOpenFilterDrawer?.(true);
                }}
              />
              {/* Create New Ticket button - only for super_admin and org_master */}
              {(checkOrganizationRole('super_admin') ||
                checkOrganizationRole('org_master')) && (
                <CustomButton
                  title="Create New Ticket"
                  startIcon={<AddIcon />}
                  onClick={handleCreateTicket}
                />
              )}
            </Box>
          </Box>
          <Divider />
        </Box>

        <Box className="section-right-content">
          <Box className="all-tickets-container-wrap">
            <Box className="all-tickets-container">
              {loading ? (
                <ContentLoader />
              ) : (
                <>
                  {/* Tickets List */}
                  <Box className="tickets-grid">
                    {ticketsData?.map((ticket) => (
                      <Box key={ticket?.id} className="ticket-card-wrapper">
                        <Box className="all-tickets-list-container">
                          <Ticket
                            ticketsList={[ticket]}
                            selectedTicket={null}
                            onTicketClick={handleTicketClick}
                            onTicketDelete={handleTicketDelete}
                            onTicketStatusChange={handleTicketStatusChange}
                            hideDropdown={true}
                            hideStatusFilter={true} // Hide status filter in All Support Tickets page
                          />
                        </Box>
                      </Box>
                    ))}
                  </Box>

                  {/* No tickets message */}
                  {ticketsData?.length === 0 && !loading && (
                    <Box className="no-data d-flex align-center justify-center">
                      <NoDataView
                        title="No Support Tickets Found"
                        description="Try adjusting your filters or create a new ticket."
                      />
                    </Box>
                  )}

                  {/* Pagination */}
                  {ticketsData?.length > 0 && (
                    <Box className="all-ticket-pagination-wrap">
                      <CustomOrgPagination
                        currentPage={currentPage}
                        totalCount={totalCount}
                        rowsPerPage={rowsPerPage}
                        onPageChange={handlePageChange}
                        OnRowPerPage={handleRowsPerPageChange}
                      />
                    </Box>
                  )}
                </>
              )}

              {/* Filter Drawer */}
              <Box className="drawer-wrap">
                <RightDrawer
                  className="filter-options-drawer"
                  anchor="right"
                  open={openFilterDrawer}
                  onClose={() => setOpenFilterDrawer?.(false)}
                  title="Filter"
                  content={
                    <Box>
                      <FilterComponent
                        filters={filters || []}
                        filterData={filterData || {}}
                        setFilterData={setFilterData}
                        selectedFilters={selectedFilters || []}
                        toggleFilter={toggleFilter}
                        saveLayout={saveLayout}
                        setOpenFilterDrawer={setOpenFilterDrawer}
                        setSelectedFilters={setSelectedFilters}
                        getFirstFourFilters={getFirstFourFilters}
                        setSearchValue={setSearchValue}
                        searchValue={searchValue || ''}
                        handleKeyPress={handleKeyPress}
                        isMobile={isMobile}
                        handleApplyFilter={handleApplyFilter}
                        handleClearFilter={handleClearFilter}
                      />
                    </Box>
                  }
                />
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
}
